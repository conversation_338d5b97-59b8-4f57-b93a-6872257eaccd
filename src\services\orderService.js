const API_URL = import.meta.env.REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

// Orders (Commandes)
export async function fetchOrders(params = {}) {
  try {
    const queryParams = new URLSearchParams();

    if (params.user_id) queryParams.append('user_id', params.user_id);
    if (params.date_debut) queryParams.append('date_debut', params.date_debut);
    if (params.date_fin) queryParams.append('date_fin', params.date_fin);
    if (params.page) queryParams.append('page', params.page);
    if (params.per_page) queryParams.append('per_page', params.per_page);

    // Try both endpoints - first the French one, then the English one if that fails
    let url = `${API_URL}/commandes${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    console.log('Fetching orders from:', url);

    let res = await fetch(url);

    // If the French endpoint fails, try the English one
    if (!res.ok) {
      url = `${API_URL}/orders${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      console.log('First endpoint failed, trying:', url);
      res = await fetch(url);
    }

    if (!res.ok) {
      throw new Error(`Failed to fetch orders. Status: ${res.status}`);
    }

    const response = await res.json();
    console.log('API returned response:', response);

    // Check if the response has the expected structure
    if (!response || !response.data) {
      throw new Error('API returned unexpected format for orders.');
    }

    // Handle the nested data structure
    let ordersData = [];

    if (response.data.data && Array.isArray(response.data.data)) {
      // The data is nested in response.data.data (paginated response)
      console.log('Found paginated data structure with', response.data.data.length, 'orders');
      ordersData = response.data.data;
    } else if (Array.isArray(response.data)) {
      // The data is directly in response.data
      console.log('Found direct data array with', response.data.length, 'orders');
      ordersData = response.data;
    } else {
      // Single order or unexpected format
      console.log('Found single order or unexpected format');
      ordersData = [response.data];
    }

    // Check if we have any orders
    // Note: Empty data is valid - the API might legitimately return no orders

    // Convert the orders
    return ordersData.map(convertOrderFromApi);
  } catch (error) {
    console.error('Error fetching orders:', error);
    throw error; // Re-throw the error to be handled by the caller
  }
}

export async function fetchOrderById(id) {
  try {
    const res = await fetch(`${API_URL}/commandes/${id}`);
    if (!res.ok) {
      throw new Error(`Failed to fetch order ${id}. Status: ${res.status}`);
    }

    const response = await res.json();
    console.log(`API returned order ${id} response:`, response);

    // Check if the response has the expected structure
    if (!response) {
      throw new Error(`API returned unexpected format for order ${id}.`);
    }

    // Handle different response formats
    let orderData = null;

    if (response.data) {
      // The order is in response.data
      orderData = response.data;
    } else if (response.status === 'success' && response.message) {
      // The order might be directly in the response
      orderData = response;
    } else {
      // Assume the response is the order itself
      orderData = response;
    }

    return convertOrderFromApi(orderData);
  } catch (error) {
    console.error(`Error fetching order ${id}:`, error);
    throw error; // Re-throw the error to be handled by the caller
  }
}

// Helper function to convert order from API format to our internal format
function convertOrderFromApi(apiOrder) {
  if (!apiOrder) return null;

  console.log('Converting API order:', apiOrder);

  // Extract customer name from email if not provided
  const customerName = apiOrder.user?.name || (apiOrder.email_commande ? apiOrder.email_commande.split('@')[0] : 'Client');

  return {
    id: apiOrder.id,
    order_number: `CMD-${apiOrder.id.toString().padStart(3, '0')}`,
    customer_id: apiOrder.user_id,
    customer_name: customerName,
    customer_email: apiOrder.email_commande || apiOrder.email || '',
    customer_phone: apiOrder.telephone_commande || apiOrder.telephone || '',

    // Address information
    shipping_name: customerName,
    shipping_address_line1: apiOrder.adresse_commande || apiOrder.adresse || '',
    shipping_city: apiOrder.ville_commande || apiOrder.ville || '',
    shipping_postal_code: apiOrder.code_postal_commande || apiOrder.code_postal || '',
    shipping_country: 'Tunisie', // Based on the data

    // Order totals
    subtotal: parseFloat(apiOrder.total_commande || apiOrder.total || 0),
    total: parseFloat(apiOrder.total_commande || apiOrder.total || 0),
    discount: parseFloat(apiOrder.remise_commande || apiOrder.remise || 0),
    shipping_cost: 0, // Not provided in API
    tax: 0, // Not provided in API

    // Status information
    status:
      apiOrder.status_label ||
      (apiOrder.status === 'en_attente'
        ? 'En attente'
        : apiOrder.status === 'en_cours'
          ? 'En cours de traitement'
          : apiOrder.status === 'expedie'
            ? 'Expédiée'
            : apiOrder.status === 'livre'
              ? 'Livrée'
              : apiOrder.status === 'annule'
                ? 'Annulée'
                : 'En attente'),
    payment_status: 'paid', // Default value, API doesn't provide payment status
    payment_method: 'Carte bancaire', // Not provided in API

    // Dates
    created_at: apiOrder.created_at,
    updated_at: apiOrder.updated_at,

    // Items - if produits is not available, create a placeholder item
    items:
      (apiOrder.produits || []).length > 0
        ? (apiOrder.produits || []).map((product) => ({
            id: product.pivot?.produit_id || product.id,
            product_name: product.nom || 'Produit',
            description: product.description || '',
            sku: (product.pivot?.produit_id || product.id).toString(),
            unit_price: parseFloat(product.pivot?.prix_unitaire || 0),
            quantity: parseInt(product.pivot?.quantite || 1),
            total_price: parseFloat(product.pivot?.prix_unitaire || 0) * parseInt(product.pivot?.quantite || 1)
          }))
        : [
            {
              id: 1,
              product_name: 'Commande #' + apiOrder.id,
              description: 'Détails non disponibles',
              sku: 'N/A',
              unit_price: parseFloat(apiOrder.total_commande || apiOrder.total || 0),
              quantity: 1,
              total_price: parseFloat(apiOrder.total_commande || apiOrder.total || 0)
            }
          ],

    // Original data
    _original: apiOrder
  };
}

export async function updateOrderStatus(id, status, notes = '') {
  // Note: The API doesn't have a specific endpoint for updating status
  // We'll use the general update endpoint
  const res = await fetch(`${API_URL}/commandes/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ status, notes })
  });
  if (!res.ok) throw new Error('Erreur lors de la mise à jour du statut de la commande');

  const data = await res.json();
  return convertOrderFromApi(data);
}

export async function updateOrder(id, data) {
  const apiData = {
    adresse_commande: data.shipping_address_line1,
    ville_commande: data.shipping_city,
    code_postal_commande: data.shipping_postal_code,
    telephone_commande: data.shipping_phone,
    email_commande: data.shipping_email
  };

  const res = await fetch(`${API_URL}/commandes/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiData)
  });
  if (!res.ok) throw new Error('Erreur lors de la mise à jour de la commande');

  const responseData = await res.json();
  return convertOrderFromApi(responseData);
}

export async function createOrder(data) {
  const apiData = {
    user_id: data.customer_id,
    adresse_commande: data.shipping_address_line1,
    ville_commande: data.shipping_city,
    code_postal_commande: data.shipping_postal_code,
    telephone_commande: data.shipping_phone,
    email_commande: data.shipping_email,
    remise_commande: data.discount,
    produits: data.items.map((item) => ({
      id: item.product_id,
      quantite: item.quantity,
      prix_unitaire: item.unit_price
    }))
  };

  const res = await fetch(`${API_URL}/commandes`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiData)
  });
  if (!res.ok) throw new Error('Erreur lors de la création de la commande');

  const responseData = await res.json();
  return convertOrderFromApi(responseData);
}

export async function deleteOrder(id) {
  const res = await fetch(`${API_URL}/commandes/${id}`, {
    method: 'DELETE'
  });
  if (!res.ok) throw new Error('Erreur lors de la suppression de la commande');
  return res.json();
}

// Client Orders
export async function fetchClientOrders(clientId) {
  const res = await fetch(`${API_URL}/clients/${clientId}/commandes`);
  if (!res.ok) throw new Error('Erreur lors du chargement des commandes du client');

  const data = await res.json();
  return Array.isArray(data) ? data.map(convertOrderFromApi) : [];
}

export async function fetchLastClientOrder(clientId) {
  const res = await fetch(`${API_URL}/clients/${clientId}/derniere-commande`);
  if (!res.ok) throw new Error('Erreur lors du chargement de la dernière commande du client');

  const data = await res.json();
  return convertOrderFromApi(data);
}

// Admin Endpoints
export async function fetchLastClientOrderAdmin(clientId) {
  const res = await fetch(`${API_URL}/v1/admin/clients/${clientId}/derniere-commande`);
  if (!res.ok) throw new Error('Erreur lors du chargement de la dernière commande du client (admin)');

  const data = await res.json();
  return convertOrderFromApi(data);
}

// Mock order statuses (not provided by the API)
export async function fetchOrderStatuses() {
  // Since there's no order-statuses endpoint in the backend, we'll always use mock data
  console.log('Using mock order statuses (no endpoint available)');

  return [
    { id: 1, name: 'En attente', color: 'warning' },
    { id: 2, name: 'En cours de traitement', color: 'info' },
    { id: 3, name: 'Expédiée', color: 'primary' },
    { id: 4, name: 'Livrée', color: 'success' },
    { id: 5, name: 'Annulée', color: 'danger' },
    { id: 6, name: 'Remboursée', color: 'secondary' }
  ];
}

// Mock order history (not provided by the API)
export async function fetchOrderHistory(orderId) {
  // Return mock history since the API doesn't provide it
  return [
    {
      id: 1,
      order_id: orderId,
      status: 'En attente',
      description: 'Commande créée',
      notes: 'Commande créée par le client',
      created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: 2,
      order_id: orderId,
      status: 'En cours de traitement',
      description: 'Paiement reçu',
      notes: 'Paiement confirmé',
      created_at: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: 3,
      order_id: orderId,
      status: 'Expédiée',
      description: 'Commande expédiée',
      notes: 'Commande expédiée via Colissimo',
      created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: 4,
      order_id: orderId,
      status: 'Livrée',
      description: 'Commande livrée',
      notes: 'Commande livrée au client',
      created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
    }
  ];
}
