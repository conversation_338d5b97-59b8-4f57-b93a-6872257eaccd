import PropTypes from 'prop-types';
import { useState, useEffect } from 'react';

// material-ui
import {
  Box,
  Tab,
  Tabs,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  Chip,
  IconButton,
  TextField,
  InputAdornment
} from '@mui/material';
import { IconUserPlus, IconSearch, IconEdit, IconTrash, IconEye } from '@tabler/icons-react';

// project imports
import MainCard from 'ui-component/cards/MainCard';
import { fetchClients } from '../../services/clientService';

// assets

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div role="tabpanel" hidden={value !== index} id={`client-tabpanel-${index}`} aria-labelledby={`client-tab-${index}`} {...other}>
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired
};

function a11yProps(index) {
  return {
    id: `client-tab-${index}`,
    'aria-controls': `client-tabpanel-${index}`
  };
}

// ==============================|| CLIENT LIST ||============================== //

const ClientList = () => {
  const [value, setValue] = useState(0);
  const [isLoading, setLoading] = useState(true);
  const [clients, setClients] = useState([]);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredClients, setFilteredClients] = useState([]);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const handleSearchChange = (event) => {
    const term = event.target.value;
    setSearchTerm(term);

    if (term === '') {
      setFilteredClients(clients);
    } else {
      const filtered = clients.filter(
        (client) =>
          client.name?.toLowerCase().includes(term.toLowerCase()) ||
          client.email?.toLowerCase().includes(term.toLowerCase()) ||
          client.phone?.toLowerCase().includes(term.toLowerCase())
      );
      setFilteredClients(filtered);
    }
  };

  const loadClients = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await fetchClients();
      setClients(data || []);
      setFilteredClients(data || []);
    } catch (err) {
      console.error('Error loading clients:', err);
      setError('Erreur lors du chargement des clients: ' + err.message);
      setClients([]);
      setFilteredClients([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadClients();
  }, []);

  const getFilteredClientsByStatus = (status) => {
    if (status === 'all') return filteredClients;
    if (status === 'active') return filteredClients.filter((client) => client.is_active !== false);
    if (status === 'inactive') return filteredClients.filter((client) => client.is_active === false);
    return filteredClients;
  };

  const renderClientTable = (clientsToShow) => {
    if (isLoading) {
      return (
        <Box display="flex" justifyContent="center" p={3}>
          <CircularProgress />
        </Box>
      );
    }

    if (error) {
      return (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      );
    }

    if (!clientsToShow || clientsToShow.length === 0) {
      return (
        <Typography variant="body1" sx={{ p: 2, textAlign: 'center' }}>
          Aucun client trouvé.
        </Typography>
      );
    }

    return (
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Nom</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Téléphone</TableCell>
              <TableCell>Statut</TableCell>
              <TableCell>Date d'inscription</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {clientsToShow.map((client) => (
              <TableRow key={client.id}>
                <TableCell>{client.name || client.first_name + ' ' + client.last_name || 'N/A'}</TableCell>
                <TableCell>{client.email || 'N/A'}</TableCell>
                <TableCell>{client.phone || 'N/A'}</TableCell>
                <TableCell>
                  <Chip
                    label={client.is_active !== false ? 'Actif' : 'Inactif'}
                    color={client.is_active !== false ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell>{client.created_at ? new Date(client.created_at).toLocaleDateString('fr-FR') : 'N/A'}</TableCell>
                <TableCell>
                  <IconButton size="small" color="primary" title="Voir">
                    <IconEye />
                  </IconButton>
                  <IconButton size="small" color="secondary" title="Modifier">
                    <IconEdit />
                  </IconButton>
                  <IconButton size="small" color="error" title="Supprimer">
                    <IconTrash />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  return (
    <MainCard
      title="Client Management"
      secondary={
        <Button variant="contained" startIcon={<IconUserPlus />}>
          Add Client
        </Button>
      }
    >
      <Box sx={{ width: '100%' }}>
        {/* Search Field */}
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Rechercher des clients..."
            value={searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconSearch />
                </InputAdornment>
              )
            }}
          />
        </Box>

        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={value} onChange={handleChange} aria-label="client list tabs">
            <Tab label="Tous les Clients" {...a11yProps(0)} />
            <Tab label="Clients Actifs" {...a11yProps(1)} />
            <Tab label="Clients Inactifs" {...a11yProps(2)} />
          </Tabs>
        </Box>

        <TabPanel value={value} index={0}>
          <Typography variant="h5" gutterBottom>
            Tous les Clients ({getFilteredClientsByStatus('all').length})
          </Typography>
          {renderClientTable(getFilteredClientsByStatus('all'))}
        </TabPanel>

        <TabPanel value={value} index={1}>
          <Typography variant="h5" gutterBottom>
            Clients Actifs ({getFilteredClientsByStatus('active').length})
          </Typography>
          {renderClientTable(getFilteredClientsByStatus('active'))}
        </TabPanel>

        <TabPanel value={value} index={2}>
          <Typography variant="h5" gutterBottom>
            Clients Inactifs ({getFilteredClientsByStatus('inactive').length})
          </Typography>
          {renderClientTable(getFilteredClientsByStatus('inactive'))}
        </TabPanel>
      </Box>
    </MainCard>
  );
};

export default ClientList;
