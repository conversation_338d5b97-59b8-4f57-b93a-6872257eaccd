const API_URL = import.meta.env.REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

// Client Groups
export async function fetchClientGroups() {
  const res = await fetch(`${API_URL}/client-groups`);
  if (!res.ok) throw new Error('Error loading client groups');
  return res.json();
}

export async function fetchClientGroupById(id) {
  const res = await fetch(`${API_URL}/client-groups/${id}`);
  if (!res.ok) throw new Error('Error loading client group');
  return res.json();
}

export async function createClientGroup(data) {
  const res = await fetch(`${API_URL}/client-groups`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Error creating client group');
  return res.json();
}

export async function updateClientGroup(id, data) {
  const res = await fetch(`${API_URL}/client-groups/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Error updating client group');
  return res.json();
}

export async function deleteClientGroup(id) {
  const res = await fetch(`${API_URL}/client-groups/${id}`, {
    method: 'DELETE'
  });
  if (!res.ok) throw new Error('Error deleting client group');
  return res.json();
}

// Clients
export async function fetchClients(params = {}) {
  const queryParams = new URLSearchParams();

  if (params.group_id) queryParams.append('group_id', params.group_id);
  if (params.search) queryParams.append('search', params.search);
  if (params.page) queryParams.append('page', params.page);
  if (params.per_page) queryParams.append('per_page', params.per_page);

  const url = `${API_URL}/clients${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

  const res = await fetch(url);
  if (!res.ok) throw new Error('Error loading clients');
  return res.json();
}

export async function fetchClientById(id) {
  const res = await fetch(`${API_URL}/clients/${id}`);
  if (!res.ok) throw new Error('Error loading client');
  return res.json();
}

export async function createClient(data) {
  const res = await fetch(`${API_URL}/clients`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Error creating client');
  return res.json();
}

export async function updateClient(id, data) {
  const res = await fetch(`${API_URL}/clients/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Error updating client');
  return res.json();
}

export async function deleteClient(id) {
  const res = await fetch(`${API_URL}/clients/${id}`, {
    method: 'DELETE'
  });
  if (!res.ok) throw new Error('Error deleting client');
  return res.json();
}

// Client Discount Profiles
export async function fetchClientDiscountProfiles() {
  const res = await fetch(`${API_URL}/client-discount-profiles`);
  if (!res.ok) throw new Error('Error loading client discount profiles');
  return res.json();
}

export async function fetchClientDiscountProfileById(id) {
  const res = await fetch(`${API_URL}/client-discount-profiles/${id}`);
  if (!res.ok) throw new Error('Error loading client discount profile');
  return res.json();
}

export async function createClientDiscountProfile(data) {
  const res = await fetch(`${API_URL}/client-discount-profiles`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Error creating client discount profile');
  return res.json();
}

export async function updateClientDiscountProfile(id, data) {
  const res = await fetch(`${API_URL}/client-discount-profiles/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Error updating client discount profile');
  return res.json();
}

export async function deleteClientDiscountProfile(id) {
  const res = await fetch(`${API_URL}/client-discount-profiles/${id}`, {
    method: 'DELETE'
  });
  if (!res.ok) throw new Error('Error deleting client discount profile');
  return res.json();
}

// Assign discount profile to client
export async function assignDiscountProfileToClient(clientId, profileId) {
  const res = await fetch(`${API_URL}/clients/${clientId}/discount-profile`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ profile_id: profileId })
  });
  if (!res.ok) throw new Error('Error assigning discount profile to client');
  return res.json();
}

// Remove discount profile from client
export async function removeDiscountProfileFromClient(clientId) {
  const res = await fetch(`${API_URL}/clients/${clientId}/discount-profile`, {
    method: 'DELETE'
  });
  if (!res.ok) throw new Error('Error removing discount profile from client');
  return res.json();
}

// Client Points of Sale
export async function fetchClientPointsOfSale(clientId) {
  const res = await fetch(`${API_URL}/clients/${clientId}/points-of-sale`);
  if (!res.ok) throw new Error('Error loading client points of sale');
  return res.json();
}

export async function assignPointOfSaleToClient(clientId, pointOfSaleId) {
  const res = await fetch(`${API_URL}/clients/${clientId}/points-of-sale`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ point_of_sale_id: pointOfSaleId })
  });
  if (!res.ok) throw new Error('Error assigning point of sale to client');
  return res.json();
}

export async function removePointOfSaleFromClient(clientId, pointOfSaleId) {
  const res = await fetch(`${API_URL}/clients/${clientId}/points-of-sale/${pointOfSaleId}`, {
    method: 'DELETE'
  });
  if (!res.ok) throw new Error('Error removing point of sale from client');
  return res.json();
}
