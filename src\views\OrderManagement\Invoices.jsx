import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Table, <PERSON><PERSON>, Al<PERSON>, Spin<PERSON>, Badge, <PERSON>dal, Row, Col, Breadcrumb, InputGroup, Form } from 'react-bootstrap';
import { FaFileInvoice, FaEye, FaPrint, FaDownload, FaHome, FaSearch, FaCalendarAlt, FaMoneyBillWave } from 'react-icons/fa';

const API_URL = import.meta.env.REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

const Invoices = () => {
  // States
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  // Modal states
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [modalLoading, setModalLoading] = useState(false);

  // Load invoices on component mount
  useEffect(() => {
    loadInvoices();
  }, []);

  const loadInvoices = async () => {
    try {
      setLoading(true);
      setError('');

      // Fetch payments which represent our invoices
      const paymentsResponse = await fetch(`${API_URL}/paiements`);
      if (!paymentsResponse.ok) {
        throw new Error('Erreur lors du chargement des paiements');
      }

      const paymentsData = await paymentsResponse.json();
      const payments = Array.isArray(paymentsData) ? paymentsData : paymentsData.data || [];

      // For each payment, fetch the related order to get complete invoice data
      const invoicesWithOrders = await Promise.all(
        payments.map(async (payment) => {
          try {
            const orderResponse = await fetch(`${API_URL}/commandes/${payment.commande_id}`);
            if (orderResponse.ok) {
              const orderData = await orderResponse.json();
              return {
                ...payment,
                order: orderData,
                invoice_number: `FAC-${payment.id.toString().padStart(4, '0')}`,
                invoice_date: payment.created_at,
                amount: payment.montant,
                status: payment.status || 'completed'
              };
            }
            return {
              ...payment,
              order: null,
              invoice_number: `FAC-${payment.id.toString().padStart(4, '0')}`,
              invoice_date: payment.created_at,
              amount: payment.montant,
              status: payment.status || 'completed'
            };
          } catch (err) {
            console.error(`Error fetching order ${payment.commande_id}:`, err);
            return {
              ...payment,
              order: null,
              invoice_number: `FAC-${payment.id.toString().padStart(4, '0')}`,
              invoice_date: payment.created_at,
              amount: payment.montant,
              status: payment.status || 'completed'
            };
          }
        })
      );

      setInvoices(invoicesWithOrders);
    } catch (err) {
      console.error('Error loading invoices:', err);
      setError('Erreur lors du chargement des factures: ' + err.message);
      setInvoices([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter invoices based on search term
  const filteredInvoices = invoices.filter(
    (invoice) =>
      invoice.invoice_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.nom_client?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.email_commande?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.transaction_id?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle view invoice details
  const handleViewInvoice = async (invoice) => {
    setSelectedInvoice(invoice);
    setShowInvoiceModal(true);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format currency
  const formatCurrency = (amount) => {
    if (!amount) return '0,00 €';
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  // Get status badge variant
  const getStatusBadge = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'complete':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  // Get status text
  const getStatusText = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'complete':
        return 'Payée';
      case 'pending':
        return 'En attente';
      case 'failed':
        return 'Échec';
      default:
        return 'Inconnue';
    }
  };

  return (
    <Container fluid className="py-4">
      {/* Header and Breadcrumb */}
      <div className="mb-4">
        <h2 className="fw-bold text-primary mb-2">
          <FaFileInvoice className="me-2" />
          Gestion des Factures
        </h2>
        <Breadcrumb>
          <Breadcrumb.Item href="/dashboard">
            <FaHome size={14} className="me-1" /> Accueil
          </Breadcrumb.Item>
          <Breadcrumb.Item active>Factures</Breadcrumb.Item>
        </Breadcrumb>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <Alert variant="success" onClose={() => setSuccess('')} dismissible>
          {success}
        </Alert>
      )}
      {error && (
        <Alert variant="danger" onClose={() => setError('')} dismissible>
          {error}
        </Alert>
      )}

      {/* Search */}
      <Card className="mb-4 shadow-sm border-0">
        <Card.Body>
          <Row className="align-items-center">
            <Col md={6}>
              <InputGroup>
                <InputGroup.Text>
                  <FaSearch />
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Rechercher une facture (numéro, client, email, transaction)..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </Col>
            <Col md={6} className="text-end">
              <Button variant="outline-primary" onClick={loadInvoices} disabled={loading} className="px-4">
                {loading ? (
                  <>
                    <Spinner size="sm" animation="border" className="me-2" />
                    Actualisation...
                  </>
                ) : (
                  'Actualiser'
                )}
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Invoices Table */}
      <Card className="shadow-sm border-0">
        <Card.Body>
          {loading ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="primary" />
              <p className="mt-2 text-muted">Chargement des factures...</p>
            </div>
          ) : filteredInvoices.length === 0 ? (
            <div className="text-center py-5">
              <FaFileInvoice size={48} className="text-muted mb-3" />
              <h5 className="text-muted">Aucune facture trouvée</h5>
              <p className="text-muted">
                {searchTerm ? 'Aucune facture ne correspond à votre recherche.' : 'Aucune facture disponible pour le moment.'}
              </p>
            </div>
          ) : (
            <div className="table-responsive">
              <Table hover className="align-middle">
                <thead className="table-light">
                  <tr>
                    <th>N° Facture</th>
                    <th>Date</th>
                    <th>Client</th>
                    <th>Commande</th>
                    <th>Montant</th>
                    <th>Méthode</th>
                    <th>Statut</th>
                    <th className="text-center">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredInvoices.map((invoice) => (
                    <tr key={invoice.id}>
                      <td>
                        <div className="fw-medium text-primary">{invoice.invoice_number}</div>
                        {invoice.transaction_id && <small className="text-muted">ID: {invoice.transaction_id}</small>}
                      </td>
                      <td>
                        <div className="small">
                          <FaCalendarAlt className="me-1" />
                          {formatDate(invoice.invoice_date)}
                        </div>
                      </td>
                      <td>
                        <div>
                          <div className="fw-medium">{invoice.order?.nom_client || invoice.order?.prenom_client || 'Client inconnu'}</div>
                          {invoice.order?.email_commande && <small className="text-muted">{invoice.order.email_commande}</small>}
                        </div>
                      </td>
                      <td>
                        <Badge bg="info" className="rounded-pill">
                          CMD-{invoice.commande_id}
                        </Badge>
                      </td>
                      <td>
                        <div className="fw-medium text-success">
                          <FaMoneyBillWave className="me-1" />
                          {formatCurrency(invoice.amount)}
                        </div>
                      </td>
                      <td>
                        <Badge bg="secondary" className="text-capitalize">
                          {invoice.methode_paiement?.replace('_', ' ') || 'Non spécifiée'}
                        </Badge>
                      </td>
                      <td>
                        <Badge bg={getStatusBadge(invoice.status)}>{getStatusText(invoice.status)}</Badge>
                      </td>
                      <td className="text-center">
                        <div className="btn-group" role="group">
                          <Button size="sm" variant="outline-info" onClick={() => handleViewInvoice(invoice)} title="Voir la facture">
                            <FaEye />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline-primary"
                            onClick={() => {
                              setSelectedInvoice(invoice);
                              setTimeout(() => window.print(), 100);
                            }}
                            title="Imprimer"
                          >
                            <FaPrint />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Invoice Details Modal */}
      <Modal show={showInvoiceModal} onHide={() => setShowInvoiceModal(false)} size="xl">
        <Modal.Header closeButton>
          <Modal.Title>Détails de la facture: {selectedInvoice?.invoice_number}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedInvoice && (
            <div className="invoice-details">
              {/* Invoice Header */}
              <div className="invoice-header mb-4 p-4 bg-light rounded">
                <Row>
                  <Col md={6}>
                    <h4 className="text-primary mb-3">jiheneLine</h4>
                    <p className="mb-1">
                      <strong>Adresse:</strong> 123 Rue de Commerce
                    </p>
                    <p className="mb-1">
                      <strong>Ville:</strong> Tunis, Tunisie
                    </p>
                    <p className="mb-1">
                      <strong>Email:</strong> <EMAIL>
                    </p>
                    <p className="mb-0">
                      <strong>Téléphone:</strong> +216 XX XXX XXX
                    </p>
                  </Col>
                  <Col md={6} className="text-end">
                    <h3 className="text-primary">FACTURE</h3>
                    <p className="mb-1">
                      <strong>N°:</strong> {selectedInvoice.invoice_number}
                    </p>
                    <p className="mb-1">
                      <strong>Date:</strong> {formatDate(selectedInvoice.invoice_date)}
                    </p>
                    <p className="mb-1">
                      <strong>Commande:</strong> CMD-{selectedInvoice.commande_id}
                    </p>
                    <Badge bg={getStatusBadge(selectedInvoice.status)} className="fs-6">
                      {getStatusText(selectedInvoice.status)}
                    </Badge>
                  </Col>
                </Row>
              </div>

              {/* Customer Information */}
              <div className="customer-info mb-4">
                <h5 className="border-bottom pb-2">Informations Client</h5>
                <Row>
                  <Col md={6}>
                    <p className="mb-1">
                      <strong>Nom:</strong> {selectedInvoice.order?.nom_client || 'Non spécifié'}
                    </p>
                    <p className="mb-1">
                      <strong>Prénom:</strong> {selectedInvoice.order?.prenom_client || 'Non spécifié'}
                    </p>
                    <p className="mb-1">
                      <strong>Email:</strong> {selectedInvoice.order?.email_commande || 'Non spécifié'}
                    </p>
                    <p className="mb-0">
                      <strong>Téléphone:</strong> {selectedInvoice.order?.telephone_commande || 'Non spécifié'}
                    </p>
                  </Col>
                  <Col md={6}>
                    <p className="mb-1">
                      <strong>Adresse:</strong> {selectedInvoice.order?.adresse_commande || 'Non spécifiée'}
                    </p>
                    <p className="mb-1">
                      <strong>Ville:</strong> {selectedInvoice.order?.ville_commande || 'Non spécifiée'}
                    </p>
                    <p className="mb-0">
                      <strong>Code postal:</strong> {selectedInvoice.order?.code_postal_commande || 'Non spécifié'}
                    </p>
                  </Col>
                </Row>
              </div>

              {/* Payment Information */}
              <div className="payment-info mb-4">
                <h5 className="border-bottom pb-2">Informations de Paiement</h5>
                <Row>
                  <Col md={6}>
                    <p className="mb-1">
                      <strong>Méthode:</strong> {selectedInvoice.methode_paiement?.replace('_', ' ') || 'Non spécifiée'}
                    </p>
                    <p className="mb-1">
                      <strong>Transaction ID:</strong> {selectedInvoice.transaction_id || 'Non disponible'}
                    </p>
                    <p className="mb-0">
                      <strong>Date de traitement:</strong> {formatDate(selectedInvoice.processed_at)}
                    </p>
                  </Col>
                  <Col md={6}>
                    <div className="text-end">
                      <h4 className="text-success">
                        <FaMoneyBillWave className="me-2" />
                        {formatCurrency(selectedInvoice.amount)}
                      </h4>
                    </div>
                  </Col>
                </Row>
              </div>

              {/* Gateway Response (if available) */}
              {selectedInvoice.gateway_response && (
                <div className="gateway-info mb-4">
                  <h5 className="border-bottom pb-2">Réponse de la Passerelle</h5>
                  <pre className="bg-light p-3 rounded small">
                    {typeof selectedInvoice.gateway_response === 'string'
                      ? selectedInvoice.gateway_response
                      : JSON.stringify(selectedInvoice.gateway_response, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowInvoiceModal(false)}>
            Fermer
          </Button>
          <Button variant="primary" onClick={() => window.print()}>
            <FaPrint className="me-2" />
            Imprimer
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Print Styles */}
      <style jsx>{`
        @media print {
          .btn,
          .breadcrumb,
          .alert {
            display: none !important;
          }
          .modal-header,
          .modal-footer {
            display: none !important;
          }
          .invoice-details {
            margin: 0 !important;
            padding: 20px !important;
          }
        }
      `}</style>
    </Container>
  );
};

export default Invoices;
