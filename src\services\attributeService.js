const API_URL = import.meta.env.REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

// Attribute Groups
export async function fetchAttributeGroups() {
  const res = await fetch(`${API_URL}/admin/groupes-attributs`);
  if (!res.ok) throw new Error("Erreur lors du chargement des groupes d'attributs");

  // Convert API response to our internal format
  const data = await res.json();
  return Array.isArray(data) ? data.map(convertAttributeGroupFromApi) : [];
}

// Helper function to convert attribute group from API format to our internal format
function convertAttributeGroupFromApi(apiGroup) {
  if (!apiGroup) return null;

  return {
    id: apiGroup.id,
    name: apiGroup.nom,
    description: apiGroup.description || '',
    attributes: Array.isArray(apiGroup.attributs) ? apiGroup.attributs.map(convertAttributeFromApi) : [],
    // Keep original data for reference
    _original: apiGroup
  };
}

export async function createAttributeGroup(data) {
  // Convert field names to match API expectations
  const apiData = {
    nom: data.name,
    description: data.description
  };

  const res = await fetch(`${API_URL}/admin/groupes-attributs`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiData)
  });
  if (!res.ok) throw new Error("Erreur lors de la création du groupe d'attributs");
  return res.json();
}

export async function updateAttributeGroup(id, data) {
  // Convert field names to match API expectations
  const apiData = {
    nom: data.name,
    description: data.description
  };

  const res = await fetch(`${API_URL}/admin/groupes-attributs/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiData)
  });
  if (!res.ok) throw new Error("Erreur lors de la mise à jour du groupe d'attributs");
  return res.json();
}

export async function deleteAttributeGroup(id) {
  const res = await fetch(`${API_URL}/admin/groupes-attributs/${id}`, { method: 'DELETE' });
  if (!res.ok) throw new Error("Erreur lors de la suppression du groupe d'attributs");
  return res.json();
}

// Attributes
export async function fetchAttributes(groupId = null) {
  const url = groupId ? `${API_URL}/admin/groupes-attributs/${groupId}/attributs` : `${API_URL}/admin/attributs`;

  const res = await fetch(url);
  if (!res.ok) throw new Error('Erreur lors du chargement des attributs');

  // Convert API response to our internal format
  const data = await res.json();
  return Array.isArray(data) ? data.map(convertAttributeFromApi) : [];
}

export async function createAttribute(data) {
  // Convert field names to match API expectations
  const apiData = {
    nom: data.name,
    description: data.description,
    type_valeur: mapAttributeType(data.type),
    groupe_id: data.attribute_group_id,
    filtrable: data.filtrable || false,
    comparable: data.comparable || false,
    obligatoire: data.obligatoire || false,
    sous_categories: data.sous_categories || []
  };

  const res = await fetch(`${API_URL}/admin/attributs`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiData)
  });
  if (!res.ok) throw new Error("Erreur lors de la création de l'attribut");

  const responseData = await res.json();
  return convertAttributeFromApi(responseData);
}

export async function updateAttribute(id, data) {
  // Convert field names to match API expectations
  const apiData = {
    nom: data.name,
    description: data.description,
    type_valeur: mapAttributeType(data.type),
    groupe_id: data.attribute_group_id,
    filtrable: data.filtrable || false,
    comparable: data.comparable || false,
    obligatoire: data.obligatoire || false,
    sous_categories: data.sous_categories || []
  };

  const res = await fetch(`${API_URL}/admin/attributs/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiData)
  });
  if (!res.ok) throw new Error("Erreur lors de la mise à jour de l'attribut");

  const responseData = await res.json();
  return convertAttributeFromApi(responseData);
}

export async function deleteAttribute(id) {
  const res = await fetch(`${API_URL}/admin/attributs/${id}`, { method: 'DELETE' });
  if (!res.ok) throw new Error("Erreur lors de la suppression de l'attribut");
  return res.json();
}

// Helper function to map attribute types from UI to API
function mapAttributeType(uiType) {
  switch (uiType) {
    case 'select':
    case 'text':
      return 'texte';
    case 'number':
      return 'nombre';
    case 'boolean':
      return 'booleen';
    case 'list':
      return 'liste';
    default:
      return 'texte';
  }
}

// Helper function to convert attribute from API format to our internal format
function convertAttributeFromApi(apiAttribute) {
  if (!apiAttribute) return null;

  return {
    id: apiAttribute.id,
    name: apiAttribute.nom,
    description: apiAttribute.description || '',
    type: mapAttributeTypeFromApi(apiAttribute.type_valeur),
    attribute_group_id: apiAttribute.groupe_id,
    filtrable: apiAttribute.filtrable || false,
    comparable: apiAttribute.comparable || false,
    obligatoire: apiAttribute.obligatoire || false,
    sous_categories: apiAttribute.sous_categories || [],
    created_at: apiAttribute.created_at,
    updated_at: apiAttribute.updated_at,
    // Keep original data for reference
    _original: apiAttribute
  };
}

// Helper function to map attribute types from API to UI
function mapAttributeTypeFromApi(apiType) {
  switch (apiType) {
    case 'texte':
      return 'text';
    case 'nombre':
      return 'number';
    case 'booleen':
      return 'boolean';
    case 'liste':
      return 'select';
    default:
      return 'select';
  }
}

// Attribute Values
export async function fetchAttributeValues(attributeId) {
  const res = await fetch(`${API_URL}/admin/attributs/${attributeId}/valeurs`);
  if (!res.ok) throw new Error("Erreur lors du chargement des valeurs d'attribut");

  // Convert API response to our internal format
  const data = await res.json();
  return Array.isArray(data) ? data.map(convertAttributeValueFromApi) : [];
}

export async function createAttributeValue(attributeId, data) {
  // Convert field names to match API expectations
  const apiData = {
    valeur: data.value,
    libelle: data.display_name
  };

  const res = await fetch(`${API_URL}/admin/attributs/${attributeId}/valeurs`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiData)
  });
  if (!res.ok) throw new Error("Erreur lors de la création de la valeur d'attribut");

  const responseData = await res.json();
  return convertAttributeValueFromApi(responseData);
}

export async function updateAttributeValue(id, data) {
  // Convert field names to match API expectations
  const apiData = {
    valeur: data.value,
    libelle: data.display_name
  };

  const res = await fetch(`${API_URL}/admin/valeurs-attribut/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiData)
  });
  if (!res.ok) throw new Error("Erreur lors de la mise à jour de la valeur d'attribut");

  const responseData = await res.json();
  return convertAttributeValueFromApi(responseData);
}

export async function deleteAttributeValue(id) {
  const res = await fetch(`${API_URL}/admin/valeurs-attribut/${id}`, { method: 'DELETE' });
  if (!res.ok) throw new Error("Erreur lors de la suppression de la valeur d'attribut");
  return res.json();
}

// Helper function to convert attribute value from API format to our internal format
function convertAttributeValueFromApi(apiValue) {
  if (!apiValue) return null;

  return {
    id: apiValue.id,
    value: apiValue.valeur,
    display_name: apiValue.libelle || apiValue.valeur,
    attribute_id: apiValue.attribut_id,
    // Keep original data for reference
    _original: apiValue
  };
}

// Product Attributes
export async function assignAttributeToProduct(productId, attributeId, valueId = null) {
  const res = await fetch(`${API_URL}/produits/${productId}/attributs`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      attribut_id: attributeId,
      valeur_id: valueId
    })
  });
  if (!res.ok) throw new Error("Erreur lors de l'assignation de l'attribut au produit");
  return res.json();
}

export async function removeAttributeFromProduct(productId, attributeId) {
  const res = await fetch(`${API_URL}/produits/${productId}/attributs/${attributeId}`, {
    method: 'DELETE'
  });
  if (!res.ok) throw new Error("Erreur lors de la suppression de l'attribut du produit");
  return res.json();
}

export async function updateProductAttributeValue(productId, attributeId, valueId) {
  const res = await fetch(`${API_URL}/produits/${productId}/attributs/${attributeId}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      valeur_id: valueId
    })
  });
  if (!res.ok) throw new Error("Erreur lors de la mise à jour de la valeur d'attribut du produit");
  return res.json();
}

// Get product attributes
export async function getProductAttributes(productId) {
  const res = await fetch(`${API_URL}/produits/${productId}/attributs`);
  if (!res.ok) throw new Error('Erreur lors du chargement des attributs du produit');

  // Convert API response to our internal format
  const data = await res.json();
  return Array.isArray(data) ? data.map(convertProductAttributeFromApi) : [];
}

// Helper function to convert product attribute from API format to our internal format
function convertProductAttributeFromApi(apiAttribute) {
  if (!apiAttribute) return null;

  return {
    id: apiAttribute.id,
    product_id: apiAttribute.produit_id,
    attribute_id: apiAttribute.attribut_id,
    attribute: apiAttribute.attribut ? convertAttributeFromApi(apiAttribute.attribut) : null,
    value_id: apiAttribute.valeur_id,
    value: apiAttribute.valeur ? convertAttributeValueFromApi(apiAttribute.valeur) : null,
    // Keep original data for reference
    _original: apiAttribute
  };
}

// Category Attributes
export async function fetchCategoryAttributes(categoryId) {
  const res = await fetch(`${API_URL}/sous_categories/${categoryId}/attributs`);
  if (!res.ok) throw new Error('Erreur lors du chargement des attributs de la catégorie');

  // Convert API response to our internal format
  const data = await res.json();
  return Array.isArray(data) ? data.map(convertAttributeFromApi) : [];
}

export async function assignAttributeToCategory(attributeId, categoryId, isRequired = false) {
  const res = await fetch(`${API_URL}/admin/attributs/${attributeId}/sous-categories/${categoryId}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      obligatoire: isRequired
    })
  });
  if (!res.ok) throw new Error("Erreur lors de l'assignation de l'attribut à la catégorie");
  return res.json();
}

export async function removeAttributeFromCategory(attributeId, categoryId) {
  const res = await fetch(`${API_URL}/admin/attributs/${attributeId}/sous-categories/${categoryId}`, {
    method: 'DELETE'
  });
  if (!res.ok) throw new Error("Erreur lors de la suppression de l'attribut de la catégorie");
  return res.json();
}
